# Tour Overlay System Implementation Plan

## Overview

This document outlines the implementation plan for a comprehensive **Tour Overlay System** that extends the existing PLG Journey System to support application tours, feature callouts, and interactive UI guidance.

## 🎯 Goals

### Primary Objectives
- **Element Highlighting**: Spotlight specific UI elements with visual emphasis
- **Smart Callouts**: Contextual tooltips and pointers that guide users
- **Tour Navigation**: Smooth step-by-step progression through UI features
- **Responsive Positioning**: Intelligent placement that adapts to screen size and element position
- **Accessibility**: Full keyboard navigation and screen reader support

### Integration Requirements
- **Extend Existing PLG Journey System**: Build on the current JourneyWizard foundation
- **Leverage Rancher Components**: Use existing Modal, Tooltip, and positioning utilities
- **Maintain Performance**: Minimal impact on page load and rendering
- **Preserve Styling**: Consistent with <PERSON><PERSON>'s design system

## 🏗️ Architecture Overview

### Core Components Structure
```
pkg/rancher-saas/components/tour/
├── TourOverlay.vue           # Main tour container and backdrop
├── TourSpotlight.vue         # Element highlighting and spotlight effects
├── TourCallout.vue           # Smart callout with arrow positioning
├── TourNavigation.vue        # Step navigation controls
├── TourProgress.vue          # Progress indicator (extends existing)
└── utils/
    ├── positioning.ts        # Smart positioning logic
    ├── highlighting.ts       # Element highlighting utilities
    └── tour-manager.ts       # Tour lifecycle management
```

### Type Definitions
```typescript
// Extend existing journey types
export interface TourStep extends JourneyStep {
  // Tour-specific properties
  target?: string | Element;           // CSS selector or DOM element
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  highlightStyle?: 'outline' | 'glow' | 'backdrop' | 'none';
  calloutContent?: string | Component;
  arrow?: boolean;
  offset?: { x: number; y: number };
  
  // Interaction options
  clickToAdvance?: boolean;
  autoAdvance?: number;           // Auto-advance after N seconds
  preventInteraction?: boolean;   // Block interaction with highlighted element
}

export interface TourDefinition extends JourneyDefinition {
  // Tour-specific configuration
  overlay?: {
    backdrop?: boolean;
    backdropColor?: string;
    zIndex?: number;
    closeOnEscape?: boolean;
    closeOnClickOutside?: boolean;
  };
  
  // Tour behavior
  autoStart?: boolean;
  restartable?: boolean;
  pausable?: boolean;
}
```

## 📋 Implementation Tasks

### Task 1: Build Tour Overlay System Foundation ⚡ IN PROGRESS

**Objectives**:
- Create core overlay infrastructure
- Implement z-index management
- Build backdrop and container system
- Set up positioning utilities

**Key Components**:

#### 1.1 TourOverlay.vue
```vue
<template>
  <Teleport to="body">
    <div
      v-if="isActive"
      class="tour-overlay"
      :class="overlayClasses"
      :style="overlayStyles"
      @keydown.esc="handleEscape"
      @click.self="handleBackdropClick"
    >
      <!-- Backdrop with cutout for highlighted element -->
      <div
        v-if="showBackdrop"
        class="tour-backdrop"
        :style="backdropStyles"
      />
      
      <!-- Spotlight highlight -->
      <TourSpotlight
        v-if="currentStep?.target"
        :target="currentStep.target"
        :style="currentStep.highlightStyle"
        :offset="currentStep.offset"
      />
      
      <!-- Callout content -->
      <TourCallout
        v-if="currentStep?.calloutContent"
        :target="currentStep.target"
        :content="currentStep.calloutContent"
        :placement="currentStep.placement"
        :show-arrow="currentStep.arrow"
      />
      
      <!-- Navigation controls -->
      <TourNavigation
        :current-step="currentStepIndex"
        :total-steps="totalSteps"
        :can-go-back="canGoBack"
        :can-go-next="canGoNext"
        @next="nextStep"
        @back="previousStep"
        @skip="skipTour"
        @close="closeTour"
      />
    </div>
  </Teleport>
</template>
```

#### 1.2 Positioning Utilities
```typescript
// pkg/rancher-saas/components/tour/utils/positioning.ts
export interface ElementPosition {
  top: number;
  left: number;
  width: number;
  height: number;
  center: { x: number; y: number };
}

export interface CalloutPosition {
  top: number;
  left: number;
  placement: 'top' | 'bottom' | 'left' | 'right';
  arrowPosition: { x: number; y: number };
}

export class TourPositioning {
  static getElementPosition(target: string | Element): ElementPosition {
    const element = typeof target === 'string' 
      ? document.querySelector(target) 
      : target;
    
    if (!element) throw new Error(`Tour target not found: ${target}`);
    
    const rect = element.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    
    return {
      top: rect.top + scrollTop,
      left: rect.left + scrollLeft,
      width: rect.width,
      height: rect.height,
      center: {
        x: rect.left + scrollLeft + rect.width / 2,
        y: rect.top + scrollTop + rect.height / 2
      }
    };
  }
  
  static calculateCalloutPosition(
    targetPos: ElementPosition,
    calloutSize: { width: number; height: number },
    placement: string = 'auto',
    offset: { x: number; y: number } = { x: 0, y: 0 }
  ): CalloutPosition {
    // Smart positioning logic that avoids viewport edges
    // Falls back to best available position if preferred placement doesn't fit
    // Returns optimal position and arrow placement
  }
  
  static ensureElementVisible(target: string | Element): Promise<void> {
    // Smooth scroll to ensure target element is visible
    // Handle cases where element is in scrollable containers
  }
}
```

**Deliverables**:
- [ ] TourOverlay.vue component with backdrop and container
- [ ] TourPositioning utility class with smart positioning
- [ ] Z-index management system (tour overlays above all other content)
- [ ] Keyboard navigation and accessibility support
- [ ] Integration with existing journey store

**Technical Considerations**:
- **Z-Index Strategy**: Use CSS custom properties for dynamic z-index management
- **Performance**: Use `transform` and `opacity` for smooth animations
- **Accessibility**: Proper focus management and ARIA attributes
- **Responsive**: Handle different screen sizes and orientations

### Task 2: Implement Element Highlighting and Spotlight

**Objectives**:
- Create visual highlighting for target elements
- Support multiple highlight styles
- Smooth animations and transitions
- Handle dynamic content and layout changes

### Task 3: Build Tour Callout and Tooltip Components

**Objectives**:
- Smart callout positioning with arrows
- Responsive behavior and viewport awareness
- Rich content support (HTML, Vue components)
- Integration with existing Rancher tooltip system

### Task 4: Create Tour Step Navigation System

**Objectives**:
- Navigation controls (next, back, skip, close)
- Progress indicators and step counters
- Smooth transitions between steps
- Integration with journey progress tracking

### Task 5: Implement Tour Definition and Configuration System

**Objectives**:
- Flexible tour definition format
- Element selector validation
- Conditional step logic
- Tour templates and presets

### Task 6: Build Tour Lifecycle Management

**Objectives**:
- Tour triggering and activation
- State management and persistence
- Integration with navigation hooks
- Error handling and recovery

### Task 7: Add Tour Analytics and Tracking

**Objectives**:
- Tour engagement metrics
- Step completion tracking
- User interaction analytics
- A/B testing support

### Task 8: Create Tour Testing and Documentation

**Objectives**:
- Comprehensive test suite
- Developer documentation
- Tour preview interface
- Best practices guide

## 🎨 Design Specifications

### Visual Design
- **Backdrop**: Semi-transparent overlay (rgba(0,0,0,0.5))
- **Spotlight**: Highlighted element with subtle glow or outline
- **Callouts**: White background, subtle shadow, rounded corners
- **Animations**: Smooth fade-in/out (300ms), scale transforms for emphasis

### Accessibility
- **Focus Management**: Trap focus within tour overlay
- **Keyboard Navigation**: Arrow keys, Enter, Escape support
- **Screen Readers**: Proper ARIA labels and live regions
- **High Contrast**: Ensure visibility in high contrast mode

## 🔧 Integration Points

### Existing Systems
- **PLG Journey Store**: Extend for tour-specific state
- **Rancher Modal System**: Use for overlay management
- **User Preferences**: Persist tour completion state
- **Navigation Hooks**: Trigger tours on page changes

### Component Extensions
- **JourneyWizard**: Add tour mode support
- **ProgressTracker**: Extend for tour progress
- **Existing Tooltips**: Enhance for tour callouts

## 📊 Success Metrics

### Functional Requirements
- [ ] Tours can highlight any UI element by selector
- [ ] Callouts position intelligently and avoid viewport edges
- [ ] Navigation works smoothly between steps
- [ ] Tours persist state across page refreshes
- [ ] Multiple tours can be defined and managed
- [ ] Tours are fully accessible via keyboard and screen readers

### Performance Requirements
- [ ] Tour overlay renders in <100ms
- [ ] Smooth 60fps animations
- [ ] No impact on page load time
- [ ] Memory usage stays under 5MB per active tour

### User Experience Requirements
- [ ] Intuitive navigation and controls
- [ ] Clear visual hierarchy and focus
- [ ] Responsive design works on all screen sizes
- [ ] Tours feel integrated with Rancher's design system

## 🚀 Implementation Priority

1. **Phase 1**: Core overlay and positioning system
2. **Phase 2**: Element highlighting and callouts
3. **Phase 3**: Navigation and progress tracking
4. **Phase 4**: Tour definitions and lifecycle
5. **Phase 5**: Analytics and testing tools

This foundation will enable powerful application tours that can guide users through complex Rancher workflows with contextual callouts and feature highlights.
