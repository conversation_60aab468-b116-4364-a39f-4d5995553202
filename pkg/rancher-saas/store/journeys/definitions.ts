/**
 * PLG Journey Definitions
 *
 * This file contains the predefined journey configurations for the PLG system.
 * These journeys guide users through key workflows and decision points.
 */

import type { JourneyDefinition } from '../../types/journey';

// First Login Journey - Guides new users through initial setup choices
const firstLoginJourney: JourneyDefinition = {
  id: 'first-login',
  name: 'Getting Started with Rancher',
  description: 'Welcome to Rancher! Let\'s help you get started with your cloud infrastructure journey.',
  version: '1.0.0',
  category: 'onboarding',

  estimatedDuration: 10,
  difficulty: 'beginner',
  tags: ['onboarding', 'first-time', 'setup'],

  allowSkip: true,
  allowRestart: true,
  persistProgress: true,
  showProgress: true,

  bannerTitle: 'Welcome to Rancher',
  bannerTitleSubtext: 'Let\'s get you started with your cloud infrastructure',

  triggers: [
    {
      event: 'first-login',
      conditions: [
        {
          type: 'system-state',
          property: 'isFirstLogin',
          operator: 'equals',
          value: true,
        },
        {
          type: 'system-state',
          property: 'clusterCount',
          operator: 'equals',
          value: 0,
        },
      ],
      priority: 10,
    },
  ],

  steps: [
    {
      id: 'welcome',
      name: 'welcome',
      label: 'Welcome',
      type: 'welcome',
      description: 'Welcome to your Rancher journey',
      content: `
        <p>Welcome to Rancher! We're excited to help you manage your Kubernetes clusters and applications.</p>
        <p>This quick tour will help you understand your options and get started with the right approach for your needs.</p>
      `,
      ready: true,
    },
    {
      id: 'choose-path',
      name: 'choose-path',
      label: 'Choose Your Path',
      type: 'decision',
      question: 'What would you like to do first?',
      description: 'Choose the option that best matches your current situation and goals.',
      choices: [
        {
          id: 'create-credential',
          label: 'Set up cloud credentials',
          description: 'Connect your cloud provider accounts (AWS, Azure, GCP) to enable cluster creation',
          icon: 'icon-key',
          next: 'credential-setup',
        },
        {
          id: 'import-cluster',
          label: 'Import existing cluster',
          description: 'Connect an existing Kubernetes cluster to Rancher for management',
          icon: 'icon-import',
          next: 'import-setup',
        },
        {
          id: 'create-cluster',
          label: 'Create new cluster',
          description: 'Create a new Kubernetes cluster using Rancher\'s provisioning',
          icon: 'icon-plus',
          next: 'cluster-creation',
          condition: {
            type: 'system-state',
            property: 'credentialCount',
            operator: 'greater-than',
            value: 0,
          },
        },
        {
          id: 'explore-features',
          label: 'Explore Rancher features',
          description: 'Take a tour of Rancher\'s capabilities and features',
          icon: 'icon-compass',
          next: 'feature-tour',
        },
      ],
      ready: true,
    },
    {
      id: 'credential-setup',
      name: 'credential-setup',
      label: 'Credential Setup',
      type: 'workflow',
      description: 'Set up your cloud provider credentials',
      action: {
        type: 'navigate',
        target: 'c-cluster-manager-cloudCredential',
        params: { cluster: 'local' },
      },
      ready: true,
    },
    {
      id: 'import-setup',
      name: 'import-setup',
      label: 'Import Cluster',
      type: 'workflow',
      description: 'Import your existing Kubernetes cluster',
      action: {
        type: 'navigate',
        target: 'c-cluster-manager-cluster-create',
        params: { cluster: 'local' },
        payload: { mode: 'import' },
      },
      ready: true,
    },
    {
      id: 'cluster-creation',
      name: 'cluster-creation',
      label: 'Create Cluster',
      type: 'workflow',
      description: 'Create a new Kubernetes cluster',
      action: {
        type: 'navigate',
        target: 'c-cluster-manager-cluster-create',
        params: { cluster: 'local' },
        payload: { mode: 'create' },
      },
      ready: true,
    },
    {
      id: 'feature-tour',
      name: 'feature-tour',
      label: 'Feature Tour',
      type: 'education',
      description: 'Learn about Rancher\'s key features',
      content: `
        <h4>Rancher Key Features</h4>
        <ul>
          <li><strong>Cluster Management:</strong> Create, import, and manage Kubernetes clusters</li>
          <li><strong>Application Deployment:</strong> Deploy and manage applications using Helm charts</li>
          <li><strong>User Management:</strong> Control access with RBAC and authentication</li>
          <li><strong>Monitoring & Logging:</strong> Built-in observability tools</li>
          <li><strong>Security:</strong> Policy management and security scanning</li>
        </ul>
      `,
      ready: true,
    },
    {
      id: 'completion',
      name: 'completion',
      label: 'Ready to Go!',
      type: 'celebration',
      description: 'You\'re all set to start using Rancher',
      content: `
        <p>Great! You've completed the initial setup. You can now:</p>
        <ul>
          <li>Create or import clusters</li>
          <li>Deploy applications</li>
          <li>Manage users and permissions</li>
          <li>Monitor your infrastructure</li>
        </ul>
        <p>Need help? Check out our documentation or community forums.</p>
      `,
      ready: true,
    },
  ],
};

// Post-Cluster Creation Journey
const postClusterJourney: JourneyDefinition = {
  id: 'post-cluster-creation',
  name: 'Configure Your New Cluster',
  description: 'Now that your cluster is ready, let\'s configure it for your workloads.',
  version: '1.0.0',
  category: 'workflow-completion',

  estimatedDuration: 15,
  difficulty: 'intermediate',
  tags: ['cluster', 'configuration', 'setup'],

  allowSkip: true,
  allowRestart: false,
  persistProgress: true,
  showProgress: true,

  bannerTitle: 'Cluster Configuration',
  bannerTitleSubtext: 'Configure your cluster for optimal performance',

  triggers: [
    {
      event: 'resource-created',
      conditions: [
        {
          type: 'system-state',
          property: 'lastCreatedResource',
          operator: 'equals',
          value: 'cluster',
        },
      ],
      priority: 8,
    },
  ],

  steps: [
    {
      id: 'celebration',
      name: 'celebration',
      label: 'Cluster Ready!',
      type: 'celebration',
      description: 'Your cluster has been successfully created',
      content: `
        <p>Congratulations! Your Kubernetes cluster is now active and ready for workloads.</p>
        <p>Let's configure some essential features to get the most out of your cluster.</p>
      `,
      ready: true,
    },
    {
      id: 'next-steps',
      name: 'next-steps',
      label: 'What\'s Next?',
      type: 'decision',
      question: 'What would you like to configure first?',
      choices: [
        {
          id: 'setup-users',
          label: 'Set up user access',
          description: 'Configure user authentication and role-based access control',
          icon: 'icon-user',
          action: {
            type: 'navigate',
            target: 'c-cluster-auth-config',
          },
        },
        {
          id: 'deploy-app',
          label: 'Deploy your first application',
          description: 'Deploy an application using Helm charts or custom manifests',
          icon: 'icon-application',
          action: {
            type: 'navigate',
            target: 'c-cluster-apps',
          },
        },
        {
          id: 'setup-monitoring',
          label: 'Enable monitoring',
          description: 'Set up Prometheus and Grafana for cluster monitoring',
          icon: 'icon-monitoring',
          action: {
            type: 'navigate',
            target: 'c-cluster-monitoring',
          },
        },
        {
          id: 'configure-storage',
          label: 'Configure storage',
          description: 'Set up persistent storage classes and volumes',
          icon: 'icon-storage',
          action: {
            type: 'navigate',
            target: 'c-cluster-storage',
          },
        },
      ],
      ready: true,
    },
  ],
};

// EKS Cluster Creation Journey (PLG Mode)

// Quick Actions Tour - used by FeatureTour demo to test overlay callouts
const quickActionsTour: JourneyDefinition = {
  id: 'quick-actions-tour',
  type: 'tour',
  name: 'Quick Actions Tour',
  description: 'Learn about quick action shortcuts',
  version: '1.0.0',
  category: 'demo',
  estimatedDuration: 2,
  difficulty: 'beginner',
  allowSkip: true,
  allowRestart: true,
  persistProgress: false,
  showProgress: true,
  triggers: [
    {
      event: 'manual-start',
      conditions: [],
      priority: 1,
    },
  ],
  overlay: {
    backdrop: true,
    backdropColor: 'rgba(0,0,0,0.5)',
    closeOnEscape: true,
    closeOnClickOutside: false,
  },
  steps: [
    {
      id: 'sidebar-intro',
      type: 'tour',
      title: 'Quick Actions',
      description: 'Discover shortcuts for common tasks',
      target: '#sidebar-title',
      calloutContent: '<h4>Quick Actions</h4><p>This sidebar contains shortcuts for the most common tasks in Rancher.</p>',
      placement: 'right',
      highlightStyle: 'outline',
      arrow: true,
    },
    {
      id: 'import-yaml',
      type: 'tour',
      title: 'Import YAML',
      description: 'Quickly deploy from YAML files',
      target: '#import-yaml',
      calloutContent: '<h4>Import YAML</h4><p>Paste or upload YAML files to quickly deploy resources to your clusters.</p>',
      placement: 'right',
      highlightStyle: 'outline',
      arrow: true,
    },
    {
      id: 'create-cluster',
      type: 'tour',
      title: 'Create Cluster',
      description: 'Add new Kubernetes clusters',
      target: '#create-cluster',
      calloutContent: '<h4>Create Cluster</h4><p>Set up new Kubernetes clusters on various cloud providers or on-premises.</p>',
      placement: 'right',
      highlightStyle: 'outline',
      arrow: true,
    },
    {
      id: 'deploy-app',
      type: 'tour',
      title: 'Deploy App',
      description: 'Install applications from the catalog',
      target: '#deploy-app',
      calloutContent: '<h4>Deploy App</h4><p>Browse and install applications from the Rancher catalog or Helm charts.</p>',
      placement: 'right',
      highlightStyle: 'outline',
      arrow: true,
    },
  ],
};
const eksClusterJourney: JourneyDefinition = {
  id: 'eks-cluster-plg',
  name: 'Create EKS Cluster (Guided)',
  description: 'Create an Amazon EKS cluster with guided assistance and smart defaults.',
  version: '1.0.0',
  category: 'workflow-completion',

  estimatedDuration: 20,
  difficulty: 'intermediate',
  tags: ['eks', 'aws', 'cluster-creation', 'plg'],

  allowSkip: false,
  allowRestart: true,
  persistProgress: true,
  showProgress: true,

  bannerTitle: 'EKS Cluster Creation',
  bannerTitleSubtext: 'Create your Amazon EKS cluster with guided assistance',

  triggers: [
    {
      event: 'navigation',
      conditions: [
        {
          type: 'route',
          property: 'name',
          operator: 'contains',
          value: 'cluster-create',
        },
        {
          type: 'route',
          property: 'query.type',
          operator: 'equals',
          value: 'amazoneks',
        },
        {
          type: 'system-state',
          property: 'isPLGMode',
          operator: 'equals',
          value: true,
        },
      ],
      priority: 9,
    },
  ],

  steps: [
    {
      id: 'welcome-eks',
      name: 'welcome-eks',
      label: 'EKS Setup',
      type: 'welcome',
      description: 'Let\'s create your Amazon EKS cluster',
      content: `
        <p>We'll guide you through creating an Amazon EKS cluster with optimal settings for your use case.</p>
        <p>This process will take about 15-20 minutes and will create a production-ready cluster.</p>
      `,
      ready: true,
    },
    {
      id: 'cluster-purpose',
      name: 'cluster-purpose',
      label: 'Cluster Purpose',
      type: 'decision',
      question: 'What will you primarily use this cluster for?',
      choices: [
        {
          id: 'development',
          label: 'Development & Testing',
          description: 'Optimized for development workflows with cost-effective settings',
          icon: 'icon-dev',
        },
        {
          id: 'production',
          label: 'Production Workloads',
          description: 'High availability and performance settings for production use',
          icon: 'icon-globe',
        },
        {
          id: 'learning',
          label: 'Learning & Experimentation',
          description: 'Minimal setup for learning Kubernetes and experimenting',
          icon: 'icon-graduation',
        },
      ],
      ready: true,
    },
    {
      id: 'configuration',
      name: 'configuration',
      label: 'Configuration',
      type: 'workflow',
      description: 'Configure your cluster settings',
      action: {
        type: 'custom',
        customHandler: async (context) => {
          // Apply smart defaults based on the chosen purpose
          const purpose = context.progress?.decisions['cluster-purpose']?.choiceId;
          // TODO: Implement smart defaults application
          return { purpose, configured: true };
        },
      },
      ready: true,
    },
  ],
};

// Export all journey definitions
const journeyDefinitions: Record<string, JourneyDefinition> = {
  [firstLoginJourney.id]: firstLoginJourney,
  [postClusterJourney.id]: postClusterJourney,
  [eksClusterJourney.id]: eksClusterJourney,
  [quickActionsTour.id]: quickActionsTour,
};

export default journeyDefinitions;
