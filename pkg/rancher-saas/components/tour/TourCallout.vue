<template>
  <div
    v-if="calloutPosition"
    ref="callout"
    class="tour-callout"
    :class="calloutClasses"
    :style="calloutStyles"
    role="dialog"
    aria-live="polite"
    :aria-label="ariaLabel"
  >
    <!-- Arrow pointer -->
    <div
      v-if="showArrow"
      class="tour-callout__arrow"
      :class="arrowClasses"
      :style="arrowStyles"
    />
    
    <!-- Content -->
    <div class="tour-callout__content">
      <!-- Close button -->
      <button
        v-if="showCloseButton"
        class="tour-callout__close"
        type="button"
        aria-label="Close tour"
        @click="$emit('close')"
      >
        <i class="icon icon-close" />
      </button>
      
      <!-- Main content -->
      <div class="tour-callout__body">
        <div
          v-if="typeof content === 'string'"
          class="tour-callout__text"
          v-html="content"
        />
        <component
          v-else-if="content"
          :is="content"
          v-bind="contentProps"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { TourPositioning } from './utils/positioning';
import type { ElementPosition, CalloutPosition } from '../../types/journey';

export default defineComponent({
  name: 'TourCallout',

  props: {
    target: {
      type: [String, Object],
      required: true,
    },
    content: {
      type: [String, Object],
      required: true,
    },
    contentProps: {
      type: Object,
      default: () => ({}),
    },
    placement: {
      type: String,
      default: 'auto',
      validator: (value: string) => ['top', 'bottom', 'left', 'right', 'auto'].includes(value),
    },
    showArrow: {
      type: Boolean,
      default: true,
    },
    showCloseButton: {
      type: Boolean,
      default: false,
    },
    elementPosition: {
      type: Object as () => ElementPosition,
      required: true,
    },
    offset: {
      type: Object,
      default: () => ({ x: 0, y: 0 }),
    },
    maxWidth: {
      type: Number,
      default: 320,
    },
    ariaLabel: {
      type: String,
      default: 'Tour callout',
    },
  },

  emits: ['close', 'positioned'],

  setup() {
    const callout = ref<HTMLElement | null>(null);
    const calloutPosition = ref<CalloutPosition | null>(null);
    const calloutSize = ref({ width: 0, height: 0 });

    return {
      callout,
      calloutPosition,
      calloutSize,
    };
  },

  computed: {
    calloutClasses(): Record<string, boolean> {
      return {
        'tour-callout--top': this.calloutPosition?.placement === 'top',
        'tour-callout--bottom': this.calloutPosition?.placement === 'bottom',
        'tour-callout--left': this.calloutPosition?.placement === 'left',
        'tour-callout--right': this.calloutPosition?.placement === 'right',
        'tour-callout--with-arrow': this.showArrow,
        'tour-callout--no-arrow': !this.showArrow,
      };
    },

    calloutStyles(): Record<string, any> {
      if (!this.calloutPosition) return {};

      return {
        position: 'absolute',
        top: `${this.calloutPosition.top}px`,
        left: `${this.calloutPosition.left}px`,
        maxWidth: `${this.maxWidth}px`,
        zIndex: TourPositioning.getTourZIndex() + 2,
        transform: 'translateZ(0)', // Force hardware acceleration
      };
    },

    arrowClasses(): Record<string, boolean> {
      return {
        'tour-callout__arrow--top': this.calloutPosition?.placement === 'bottom',
        'tour-callout__arrow--bottom': this.calloutPosition?.placement === 'top',
        'tour-callout__arrow--left': this.calloutPosition?.placement === 'right',
        'tour-callout__arrow--right': this.calloutPosition?.placement === 'left',
      };
    },

    arrowStyles(): Record<string, any> {
      if (!this.calloutPosition?.arrowPosition) return {};

      const { x, y } = this.calloutPosition.arrowPosition;
      
      return {
        transform: `translate(${x}px, ${y}px)`,
      };
    },
  },

  watch: {
    elementPosition: {
      handler() {
        this.updatePosition();
      },
      immediate: true,
    },
    
    placement() {
      this.updatePosition();
    },
    
    content() {
      // Recalculate position when content changes
      nextTick(() => {
        this.updatePosition();
      });
    },
  },

  mounted() {
    this.updatePosition();
    this.setupResizeObserver();
  },

  unmounted() {
    this.cleanupResizeObserver();
  },

  methods: {
    async updatePosition() {
      await nextTick();
      
      if (!this.callout || !this.elementPosition) return;

      // Measure callout size
      this.calloutSize = {
        width: this.callout.offsetWidth || this.maxWidth,
        height: this.callout.offsetHeight || 200, // Fallback height
      };

      // Calculate optimal position
      this.calloutPosition = TourPositioning.calculateCalloutPosition(
        this.elementPosition,
        this.calloutSize,
        this.placement,
        this.offset
      );

      this.$emit('positioned', {
        position: this.calloutPosition,
        size: this.calloutSize,
      });
    },

    setupResizeObserver() {
      if (!window.ResizeObserver || !this.callout) return;

      const resizeObserver = new ResizeObserver(() => {
        this.updatePosition();
      });

      resizeObserver.observe(this.callout);
      
      // Store reference for cleanup
      (this as any).resizeObserver = resizeObserver;
    },

    cleanupResizeObserver() {
      if ((this as any).resizeObserver) {
        (this as any).resizeObserver.disconnect();
        (this as any).resizeObserver = null;
      }
    },
  },
});
</script>

<style lang="scss" scoped>
.tour-callout {
  background: white;
  border-radius: 8px;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  opacity: 0;
  transform: scale(0.95);
  animation: tour-callout-enter 0.3s ease forwards;
  pointer-events: auto;

  &__arrow {
    position: absolute;
    width: 12px;
    height: 12px;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transform-origin: center;

    &--top {
      top: -6px;
      transform: rotate(45deg);
      border-bottom: none;
      border-right: none;
    }

    &--bottom {
      bottom: -6px;
      transform: rotate(45deg);
      border-top: none;
      border-left: none;
    }

    &--left {
      left: -6px;
      transform: rotate(45deg);
      border-top: none;
      border-right: none;
    }

    &--right {
      right: -6px;
      transform: rotate(45deg);
      border-bottom: none;
      border-left: none;
    }
  }

  &__content {
    position: relative;
    padding: 16px;
  }

  &__close {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--muted);
    transition: all 0.2s ease;

    &:hover {
      background: var(--hover);
      color: var(--body-text);
    }

    &:focus {
      outline: 2px solid var(--primary);
      outline-offset: 1px;
    }
  }

  &__body {
    margin-right: 24px; // Space for close button
  }

  &__text {
    font-size: 14px;
    line-height: 1.5;
    color: var(--body-text);

    h1, h2, h3, h4, h5, h6 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--body-text);
    }

    p {
      margin: 0 0 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    strong {
      font-weight: 600;
      color: var(--body-text);
    }

    code {
      background: var(--code-bg);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: var(--font-family-mono);
      font-size: 13px;
    }
  }

  // Placement-specific styles
  &--top {
    margin-bottom: 12px;
  }

  &--bottom {
    margin-top: 12px;
  }

  &--left {
    margin-right: 12px;
  }

  &--right {
    margin-left: 12px;
  }
}

// Animations
@keyframes tour-callout-enter {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .tour-callout {
    background: var(--nav-bg);
    border: 1px solid var(--border);

    &__arrow {
      background: var(--nav-bg);
      border-color: var(--border);
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .tour-callout {
    border: 2px solid currentColor;
    
    &__arrow {
      border-width: 2px;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .tour-callout {
    animation: none;
    opacity: 1;
    transform: scale(1);
  }
}
</style>
