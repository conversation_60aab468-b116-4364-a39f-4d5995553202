<template>
  <div class="feature-tour-demo">
    <h2>Feature Tour Demo</h2>
    <p>This demonstrates how to create application tours with callouts and highlights.</p>

    <!-- Tour Notification Banner -->
    <Banner
      v-if="currentTourNotification"
      :color="currentTourNotification.color"
      :closable="true"
      @close="closeTourNotification"
    >
      <div class="tour-notification-content">
        <h4>{{ currentTourNotification.title }}</h4>
        <p>{{ currentTourNotification.description }}</p>
        <div class="tour-notification-actions">
          <button
            class="btn role-primary btn-sm"
            @click="nextTourStep"
          >
            Next
          </button>
          <button
            class="btn role-secondary btn-sm"
            @click="skipTour"
          >
            Skip Tour
          </button>
        </div>
      </div>
    </Banner>

    <!-- Demo UI elements to tour -->
    <div class="demo-interface">
      <header class="demo-header">
        <h1 id="demo-title">Rancher Dashboard</h1>
        <nav class="demo-nav">
          <button
            id="clusters-btn"
            class="btn role-secondary btn-sm"
            :class="{ 'tour-active': activeTourButton === 'clusters' }"
            @click="setActiveButton('clusters')"
          >
            Clusters
          </button>
          <button
            id="workloads-btn"
            class="btn role-secondary btn-sm"
            :class="{ 'tour-active': activeTourButton === 'workloads' }"
            @click="setActiveButton('workloads')"
          >
            Workloads
          </button>
          <button
            id="apps-btn"
            class="btn role-secondary btn-sm"
            :class="{ 'tour-active': activeTourButton === 'apps' }"
            @click="setActiveButton('apps')"
          >
            Apps
          </button>
        </nav>
        <div class="demo-actions">
          <button id="create-btn" class="btn role-primary">Create</button>
          <button id="settings-btn" class="btn role-ghost">
            <i class="icon icon-gear" />
          </button>
        </div>
      </header>

      <main class="demo-content">
        <div class="demo-sidebar">
          <h3 id="sidebar-title">Quick Actions</h3>
          <ul class="demo-menu">
            <li><a id="import-yaml" href="#">Import YAML</a></li>
            <li><a id="create-cluster" href="#">Create Cluster</a></li>
            <li><a id="deploy-app" href="#">Deploy App</a></li>
          </ul>
        </div>

        <div class="demo-main">
          <div class="demo-card" id="cluster-overview">
            <h4>Cluster Overview</h4>
            <p>Monitor your Kubernetes clusters</p>
            <div class="demo-stats">
              <div class="stat" id="cluster-count">
                <span class="stat-value">3</span>
                <span class="stat-label">Clusters</span>
              </div>
              <div class="stat" id="node-count">
                <span class="stat-value">12</span>
                <span class="stat-label">Nodes</span>
              </div>
            </div>
          </div>

          <div class="demo-card" id="workload-overview">
            <h4>Workload Status</h4>
            <p>View your running applications</p>
            <div class="demo-progress" id="workload-health">
              <div class="progress-bar">
                <div class="progress-fill" style="width: 85%"></div>
              </div>
              <span>85% Healthy</span>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Tour Controls -->
    <div class="tour-controls">
      <h3>Tour Controls</h3>
      <div class="control-group">
        <button
          class="btn role-primary"
          :class="{ 'tour-active': activeTourId === 'feature-tour' }"
          @click="startFeatureTour"
        >
          Start Feature Tour
        </button>
        <button
          class="btn role-secondary"
          :class="{ 'tour-active': activeTourId === 'quick-actions-tour' }"
          @click="startQuickActionsTour"
        >
          Quick Actions Tour
        </button>
        <button
          class="btn role-tertiary"
          :class="{ 'tour-active': !activeTourId && tourStopped }"
          @click="stopTour"
        >
          Stop Tour
        </button>
      </div>

      <div class="tour-options">
        <label class="checkbox-container">
          <input
            v-model="tourOptions.showBackdrop"
            type="checkbox"
            class="checkbox-custom"
          >
          <span class="checkbox-label">Show Backdrop</span>
        </label>
        <label class="checkbox-container">
          <input
            v-model="tourOptions.autoAdvance"
            type="checkbox"
            class="checkbox-custom"
          >
          <span class="checkbox-label">Auto Advance (3s)</span>
        </label>
        <label class="radio-container">
          <input
            v-model="tourOptions.highlightStyle"
            type="radio"
            value="outline"
            class="radio-custom"
          >
          <span class="radio-label">Outline Highlight</span>
        </label>
        <label class="radio-container">
          <input
            v-model="tourOptions.highlightStyle"
            type="radio"
            value="glow"
            class="radio-custom"
          >
          <span class="radio-label">Glow Highlight</span>
        </label>
      </div>
    </div>

    <!-- Tour Overlay Component -->
    <TourOverlay
      v-if="activeTourId"
      :tour-id="activeTourId"
      @tour-completed="handleTourCompleted"
      @tour-skipped="handleTourSkipped"
      @tour-closed="handleTourClosed"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import TourOverlay from '../TourOverlay.vue';
import Banner from '@components/Banner/Banner.vue';
import type { TourDefinition, TourStep } from '../../../types/journey';

export default defineComponent({
  name: 'FeatureTour',

  components: {
    TourOverlay,
    Banner,
  },

  setup() {
    const activeTourId = ref<string | null>(null);
    const currentTour = ref<TourDefinition | null>(null);
    const currentStepIndex = ref<number>(0);
    const isOverlayVisible = ref<boolean>(false);
    const currentTourNotification = ref<any>(null);
    const activeTourButton = ref<string>('');
    const tourStopped = ref<boolean>(false);
    const tourOptions = ref({
      showBackdrop: true,
      autoAdvance: false,
      highlightStyle: 'outline',
    });

    return {
      activeTourId,
      currentTour,
      currentStepIndex,
      isOverlayVisible,
      currentTourNotification,
      activeTourButton,
      tourStopped,
      tourOptions,
    };
  },
  methods: {
    async startFeatureTour() {
      console.log('🎯 Starting Feature Tour Demo...');
      const tour = this.createFeatureTour();

      // Start the tour directly without store dependency
      this.activeTourId = tour.id;
      this.currentTour = tour;
      this.currentStepIndex = 0;
      this.tourStopped = false;

      // Show first step
      this.showTourStep(tour.steps[0]);
    },

    async startQuickActionsTour() {
      console.log('🎯 Starting Quick Actions Tour Demo...');

      // Prefer built-in journey overlay implementation for correct positioning
      try {
        await this.$store.dispatch('journeys/startJourney', { journeyId: 'quick-actions-tour' });
        this.activeTourId = 'quick-actions-tour';
        this.currentTour = null; // Overlay controls journey
        this.tourStopped = false;
      } catch (e) {
        console.warn('Falling back to local demo tour due to store error:', e);
        const tour = this.createQuickActionsTour();
        this.activeTourId = tour.id;
        this.currentTour = tour;
        this.currentStepIndex = 0;
        this.tourStopped = false;
        this.showTourStep(tour.steps[0]);
      }
    },

    stopTour() {
      console.log('🎯 Stopping Tour Demo...');
      this.activeTourId = null;
      this.currentTour = null;
      this.currentStepIndex = 0;
      this.isOverlayVisible = false;
      this.currentTourNotification = null;
      this.activeTourButton = '';
      this.tourStopped = true;
    },

    setActiveButton(buttonName: string) {
      this.activeTourButton = buttonName;
      console.log(`🎯 ${buttonName} button clicked`);
    },

    closeTourNotification() {
      this.currentTourNotification = null;
    },

    nextTourStep() {
      console.log('🎯 Next tour step...');
      this.currentTourNotification = null;
      // In a real implementation, this would advance to the next step
    },

    skipTour() {
      console.log('🎯 Skipping tour...');
      this.stopTour();
    },

    showTourStep(step: TourStep) {
      console.log('🎯 Showing tour step:', step.id);
      this.isOverlayVisible = true;

      // Highlight the target element if specified
      if (step.target) {
        this.highlightElement(step.target);
      }

      // Show callout with positioning
      this.showCallout(step);

      // Show backdrop if enabled
      if (this.tourOptions.showBackdrop) {
        this.showBackdrop();
      }
    },

    highlightElement(selector: string) {
      // Remove existing highlights
      document.querySelectorAll('.tour-highlight').forEach(el => {
        el.classList.remove('tour-highlight', 'tour-highlight-outline', 'tour-highlight-glow');
      });

      // Add highlight to target element
      const element = document.querySelector(selector);
      if (element) {
        element.classList.add('tour-highlight', `tour-highlight-${this.tourOptions.highlightStyle}`);

        // Scroll element into view
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center'
        });
      }
    },

    showCallout(step: TourStep) {
      console.log('📢 Tour Callout:', step.title, '-', step.description);

      // Show positioned callout instead of just banner
      this.showPositionedCallout(step);

      // Also show banner notification for fallback
      this.showTourNotification(step);
    },

    showTourNotification(step: TourStep) {
      // Use Banner component instead of creating DOM elements
      this.currentTourNotification = {
        title: step.title,
        description: step.description,
        color: 'info', // Use Rancher's info color for tour notifications
      };

      // Auto-remove after 5 seconds if not manually closed
      setTimeout(() => {
        if (this.currentTourNotification &&
            this.currentTourNotification.title === step.title) {
          this.currentTourNotification = null;
        }
      }, 5000);
    },

    showPositionedCallout(step: TourStep) {
      // Remove existing callouts (both local and overlay styles)
      document.querySelectorAll('.tour-callout, .feature-tour-callout').forEach(el => el.remove());

      // Find target element with better selector logic
      let targetElement = step.target ? document.querySelector(step.target) : null;

      // Fallback selectors for import-yaml
      if (!targetElement && step.id === 'import-yaml') {
        targetElement = document.querySelector('#import-yaml') ||
                       document.querySelector('a[href="#"]:contains("Import YAML")') ||
                       document.querySelector('.demo-menu a:first-child');
      }

      if (!targetElement) {
        console.warn(`Target element not found: ${step.target}`);
        return;
      }

      // Create card component
      const callout = document.createElement('div');
      callout.className = 'feature-tour-callout';

      callout.innerHTML = `
        <div class="feature-tour-card">
          <div class="feature-tour-card__header">
            <h5 class="feature-tour-card__title">${step.title}</h5>
            <button class="feature-tour-callout__close" aria-label="Close">
              <i class="icon icon-close"></i>
            </button>
          </div>
          <div class="feature-tour-card__body">
            <p class="feature-tour-card__text">${step.description}</p>
          </div>
          <div class="feature-tour-card__footer">
            <div class="feature-tour-callout__actions">
              <button class="btn role-secondary btn-sm tour-prev" ${this.currentStepIndex === 0 ? 'disabled' : ''}>
                Previous
              </button>
              <span class="feature-tour-callout__progress">${this.currentStepIndex + 1} of ${this.currentTour?.steps.length || 1}</span>
              <button class="btn role-primary btn-sm tour-next">
                Next
              </button>
            </div>
          </div>
        </div>
        <div class="feature-tour-callout__arrow"></div>
      `;

      // Position callout relative to target
      document.body.appendChild(callout);
      this.positionCallout(callout, targetElement, (step as any).placement || 'right');

      // Add event listeners
      const nextBtn = callout.querySelector('.tour-next');
      const prevBtn = callout.querySelector('.tour-prev');
      const closeBtn = callout.querySelector('.feature-tour-callout__close');

      if (nextBtn) {
        nextBtn.addEventListener('click', () => this.nextStep());
      }
      if (prevBtn) {
        prevBtn.addEventListener('click', () => this.previousStep());
      }
      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          callout.remove();
        });
      }
    },

    positionCallout(callout: HTMLElement, target: Element, position: string = 'right') {
      const targetRect = target.getBoundingClientRect();
      const viewport = {
        width: window.innerWidth,
        height: window.innerHeight
      };

      // Wait for callout to be rendered to get accurate dimensions
      setTimeout(() => {
        const calloutRect = callout.getBoundingClientRect();
        let left, top, arrowClass;

        // Calculate position based on preference
        switch (position) {
          case 'right':
            left = targetRect.right + 15;
            top = targetRect.top + (targetRect.height / 2) - (calloutRect.height / 2);
            arrowClass = 'arrow-left';

            // Check if it fits, otherwise try left
            if (left + calloutRect.width > viewport.width - 20) {
              left = targetRect.left - calloutRect.width - 15;
              arrowClass = 'arrow-right';
            }
            break;

          case 'bottom':
          default:
            left = targetRect.left + (targetRect.width / 2) - (calloutRect.width / 2);
            top = targetRect.bottom + 15;
            arrowClass = 'arrow-top';

            // Check if it fits, otherwise try top
            if (top + calloutRect.height > viewport.height - 20) {
              top = targetRect.top - calloutRect.height - 15;
              arrowClass = 'arrow-bottom';
            }
            break;
        }

        // Ensure callout stays within viewport bounds
        left = Math.max(20, Math.min(left, viewport.width - calloutRect.width - 20));
        top = Math.max(20, Math.min(top, viewport.height - calloutRect.height - 20));

        // Apply position
        callout.style.position = 'fixed';
        callout.style.left = `${left}px`;
        callout.style.top = `${top}px`;
        callout.style.zIndex = '10001';

        // Position arrow
        const arrow = callout.querySelector('.tour-callout-arrow');
        if (arrow) {
          arrow.className = `tour-callout-arrow ${arrowClass}`;
        }
      }, 10);
    },

    showBackdrop() {
      // Remove existing backdrop
      document.querySelectorAll('.tour-backdrop').forEach(el => el.remove());

      const backdrop = document.createElement('div');
      backdrop.className = 'tour-backdrop';
      backdrop.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        pointer-events: none;
      `;

      document.body.appendChild(backdrop);
    },

    nextStep() {
      if (!this.currentTour || this.currentStepIndex >= this.currentTour.steps.length - 1) {
        this.handleTourCompleted();
        return;
      }

      this.currentStepIndex++;
      this.showTourStep(this.currentTour.steps[this.currentStepIndex]);
    },

    previousStep() {
      if (this.currentStepIndex <= 0) return;

      this.currentStepIndex--;
      this.showTourStep(this.currentTour.steps[this.currentStepIndex]);
    },

    cleanupTour() {
      // Remove highlights
      document.querySelectorAll('.tour-highlight').forEach(el => {
        el.classList.remove('tour-highlight', 'tour-highlight-outline', 'tour-highlight-glow');
      });

      // Remove callouts
      document.querySelectorAll('.tour-callout').forEach(el => el.remove());

      // Remove backdrop
      document.querySelectorAll('.tour-backdrop').forEach(el => el.remove());

      // Clear notification
      this.currentTourNotification = null;
    },

    handleTourCompleted() {
      console.log('🎉 Tour completed!');
      this.cleanupTour();
      this.stopTour();
    },

    handleTourSkipped() {
      console.log('⏭️ Tour skipped!');
      this.cleanupTour();
      this.stopTour();
    },

    handleTourClosed() {
      console.log('❌ Tour closed!');
      this.cleanupTour();
      this.stopTour();
    },

    createFeatureTour(): TourDefinition {
      const steps: TourStep[] = [
        {
          id: 'welcome',
          type: 'welcome',
          title: 'Welcome to Rancher!',
          description: 'Let\'s take a quick tour of the main features.',
          calloutContent: '<h3>Welcome to Rancher!</h3><p>This tour will show you the key features of the Rancher dashboard.</p>',
          placement: 'center',
          autoAdvance: this.tourOptions.autoAdvance ? 3 : undefined,
        },
        {
          id: 'navigation',
          type: 'tour',
          title: 'Navigation Menu',
          description: 'Access different sections of Rancher',
          target: '#clusters-btn',
          calloutContent: '<h4>Navigation</h4><p>Use these buttons to navigate between different sections like Clusters, Workloads, and Apps.</p>',
          placement: 'bottom',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
        {
          id: 'create-button',
          type: 'tour',
          title: 'Create Resources',
          description: 'Create new Kubernetes resources',
          target: '#create-btn',
          calloutContent: '<h4>Create Button</h4><p>Click here to create new resources like clusters, namespaces, or deployments.</p>',
          placement: 'bottom',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
        {
          id: 'cluster-overview',
          type: 'tour',
          title: 'Cluster Overview',
          description: 'Monitor your clusters',
          target: '#cluster-overview',
          calloutContent: '<h4>Cluster Overview</h4><p>This card shows a summary of your Kubernetes clusters and their health status.</p>',
          placement: 'top',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
        {
          id: 'settings',
          type: 'tour',
          title: 'Settings',
          description: 'Configure your preferences',
          target: '#settings-btn',
          calloutContent: '<h4>Settings</h4><p>Access user preferences, cluster settings, and system configuration here.</p>',
          placement: 'left',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
        {
          id: 'completion',
          type: 'celebration',
          title: 'Tour Complete!',
          description: 'You\'ve completed the feature tour',
          calloutContent: '<h3>🎉 Great job!</h3><p>You\'ve completed the Rancher feature tour. You\'re ready to start managing your Kubernetes infrastructure!</p>',
          placement: 'center',
        },
      ];

      return {
        id: 'feature-tour',
        type: 'tour',
        title: 'Rancher Feature Tour',
        description: 'Learn about the main features of Rancher',
        category: 'onboarding',
        steps,
        triggers: [
          {
            event: 'manual-start',
            conditions: [],
          },
        ],
        overlay: {
          backdrop: this.tourOptions.showBackdrop,
          backdropColor: 'rgba(0, 0, 0, 0.5)',
          closeOnEscape: true,
          closeOnClickOutside: false,
        },
        metadata: {
          estimatedDuration: 120, // 2 minutes
          difficulty: 'beginner',
          tags: ['onboarding', 'features', 'navigation'],
        },
      };
    },

    createQuickActionsTour(): TourDefinition {
      const steps: TourStep[] = [
        {
          id: 'sidebar-intro',
          type: 'tour',
          title: 'Quick Actions',
          description: 'Discover shortcuts for common tasks',
          target: '#sidebar-title',
          calloutContent: '<h4>Quick Actions</h4><p>This sidebar contains shortcuts for the most common tasks in Rancher.</p>',
          placement: 'right',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
        {
          id: 'import-yaml',
          type: 'tour',
          title: 'Import YAML',
          description: 'Quickly deploy from YAML files',
          target: '#import-yaml',
          calloutContent: '<h4>Import YAML</h4><p>Paste or upload YAML files to quickly deploy resources to your clusters.</p>',
          placement: 'right',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
        {
          id: 'create-cluster',
          type: 'tour',
          title: 'Create Cluster',
          description: 'Add new Kubernetes clusters',
          target: '#create-cluster',
          calloutContent: '<h4>Create Cluster</h4><p>Set up new Kubernetes clusters on various cloud providers or on-premises.</p>',
          placement: 'right',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
        {
          id: 'deploy-app',
          type: 'tour',
          title: 'Deploy App',
          description: 'Install applications from the catalog',
          target: '#deploy-app',
          calloutContent: '<h4>Deploy App</h4><p>Browse and install applications from the Rancher catalog or Helm charts.</p>',
          placement: 'right',
          highlightStyle: this.tourOptions.highlightStyle as any,
          arrow: true,
        },
      ];

      return {
        id: 'quick-actions-tour',
        type: 'tour',
        title: 'Quick Actions Tour',
        description: 'Learn about quick action shortcuts',
        category: 'productivity',
        steps,
        triggers: [
          {
            event: 'manual-start',
            conditions: [],
          },
        ],
        overlay: {
          backdrop: this.tourOptions.showBackdrop,
          backdropColor: 'rgba(0, 0, 0, 0.3)',
          closeOnEscape: true,
          closeOnClickOutside: false,
        },
        metadata: {
          estimatedDuration: 60, // 1 minute
          difficulty: 'beginner',
          tags: ['productivity', 'shortcuts', 'actions'],
        },
      };
    },

    handleTourCompleted(event: any) {
      console.log('Tour completed:', event);
      this.activeTourId = null;
    },

    handleTourSkipped(event: any) {
      console.log('Tour skipped:', event);
      this.activeTourId = null;
    },

    handleTourClosed(event: any) {
      console.log('Tour closed:', event);
      this.activeTourId = null;
    },
  },
});
</script>

<style lang="scss" scoped>
.feature-tour-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  color: var(--body-text);
}

.demo-interface {
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin: 20px 0;
  background: var(--body-bg);
}

.demo-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: var(--nav-bg);
  border-bottom: 1px solid var(--border);
  gap: 20px;

  h1 {
    margin: 0;
    font-size: 18px;
    color: var(--body-text);
  }

  .demo-nav {
    display: flex;
    gap: 8px;
    margin-left: auto;
  }

  .demo-actions {
    display: flex;
    gap: 8px;
  }
}

// Tour notification styling
.tour-notification-content {
  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--body-text);
  }

  p {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--body-text);
    line-height: 1.4;
  }

  .tour-notification-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

.demo-content {
  display: flex;
  min-height: 400px;
}

.demo-sidebar {
  width: 200px;
  padding: 20px;
  background: var(--nav-bg);
  border-right: 1px solid var(--border);

  h3 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--body-text);
  }

  .demo-menu {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 8px;
    }

    a {
      display: block;
      padding: 8px 12px;
      color: var(--body-text);
      text-decoration: none;
      border-radius: var(--border-radius);
      transition: background 0.2s ease;

      &:hover {
        background: var(--accent-btn);
      }
    }
  }
}

.demo-main {
  flex: 1;
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.demo-card {
  padding: 20px;
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  background: var(--body-bg);

  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--body-text);
  }

  p {
    margin: 0 0 16px 0;
    color: var(--text-muted);
    font-size: 14px;
  }
}

.demo-stats {
  display: flex;
  gap: 20px;
}

.stat {
  text-align: center;

  .stat-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: var(--primary);
  }

  .stat-label {
    display: block;
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 4px;
  }
}

.demo-progress {
  display: flex;
  align-items: center;
  gap: 12px;

  .progress-bar {
    flex: 1;
    height: 8px;
    background: var(--border);
    border-radius: var(--border-radius);
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: var(--success);
    border-radius: var(--border-radius);
  }

  span {
    font-size: 14px;
    font-weight: 600;
    color: var(--success);
  }
}

.tour-controls {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  background: var(--nav-bg);

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: var(--body-text);
  }

  .control-group {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }

  .tour-options {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }
}

/* Tour Highlight Styles */
.tour-highlight {
  position: relative;
  z-index: 9999;
  transition: all 0.3s ease;
}

.tour-highlight-outline {
  outline: 3px solid var(--primary) !important;
  outline-offset: 2px;
  border-radius: var(--border-radius);
}

.tour-highlight-glow {
  box-shadow: 0 0 0 3px var(--primary-light-bg), 0 0 20px var(--primary-banner-bg) !important;
  border-radius: var(--border-radius);
}

/* Tour Callout Styles */
.feature-tour-callout {
  position: fixed;
  max-width: 350px;
  z-index: 10001;
  font-family: var(--font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
}

.feature-tour-card {
  background: var(--body-bg, #ffffff) !important;
  border: 1px solid var(--border, #d1d5db);
  border-radius: var(--border-radius, 8px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  margin: 0;

  /* Fallback for dark themes */
  @media (prefers-color-scheme: dark) {
    background: #1f2937 !important;
    border-color: #374151;
    color: #f9fafb;
  }
}

.feature-tour-card__header {
  padding: 1rem;
  border-bottom: 1px solid var(--border, #e5e7eb);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-tour-card__body {
  padding: 1rem;
}

.feature-tour-card__footer {
  padding: 1rem;
  border-top: 1px solid var(--border, #e5e7eb);
  background-color: var(--accent-bg, #f9fafb);
}

.feature-tour-callout__actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-tour-callout__progress {
  margin-left: auto;
  font-size: 12px;
  color: var(--muted);
}

.feature-tour-card__title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--body-text);
  line-height: 1.3;
}

.feature-tour-callout__close {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--muted);
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  line-height: 1;

  &:hover {
    color: var(--body-text);
  }
}

.feature-tour-card__text {
  margin: 0 0 16px 0;
  color: var(--body-text);
  font-size: 14px;
  line-height: 1.4;
}

.feature-tour-callout__arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
}

.feature-tour-callout__arrow.arrow-top {
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: var(--body-bg, #ffffff);

  @media (prefers-color-scheme: dark) {
    border-bottom-color: #1f2937;
  }
}

.feature-tour-callout__arrow.arrow-bottom {
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: var(--body-bg, #ffffff);

  @media (prefers-color-scheme: dark) {
    border-top-color: #1f2937;
  }
}

.feature-tour-callout__arrow.arrow-left {
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: var(--body-bg, #ffffff);

  @media (prefers-color-scheme: dark) {
    border-right-color: #1f2937;
  }
}

.feature-tour-callout__arrow.arrow-right {
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: var(--body-bg, #ffffff);

  @media (prefers-color-scheme: dark) {
    border-left-color: #1f2937;
  }
}

/* Tour Backdrop */
.tour-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

/* Button Active States */
.tour-active {
  background: var(--primary) !important;
  color: var(--primary-text) !important;
  border-color: var(--primary) !important;
}

/* Form Controls Styling */
.checkbox-container,
.radio-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--body-text);
  cursor: pointer;
}

.checkbox-custom,
.radio-custom {
  margin: 0;
  cursor: pointer;
}

</style>

<!-- Global styles specifically for dynamically injected tour elements -->
<style lang="scss">
.tour-backdrop { pointer-events: none; }
</style>
