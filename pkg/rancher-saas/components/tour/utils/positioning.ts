/**
 * Tour Positioning Utilities
 * 
 * Smart positioning system for tour overlays, callouts, and element highlighting.
 * Handles viewport awareness, responsive behavior, and smooth scrolling.
 */

import type { ElementPosition, CalloutPosition } from '../../../types/journey';

export class TourPositioning {
  private static readonly VIEWPORT_PADDING = 20;
  private static readonly ARROW_SIZE = 12;
  private static readonly CALLOUT_OFFSET = 16;

  /**
   * Get the position and dimensions of a target element
   */
  static getElementPosition(target: string | Element): ElementPosition {
    const element = typeof target === 'string' 
      ? document.querySelector(target) 
      : target;
    
    if (!element) {
      throw new Error(`Tour target not found: ${target}`);
    }
    
    const rect = element.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    
    return {
      top: rect.top + scrollTop,
      left: rect.left + scrollLeft,
      width: rect.width,
      height: rect.height,
      center: {
        x: rect.left + scrollLeft + rect.width / 2,
        y: rect.top + scrollTop + rect.height / 2
      }
    };
  }

  /**
   * Calculate optimal callout position with intelligent placement
   */
  static calculateCalloutPosition(
    targetPos: ElementPosition,
    calloutSize: { width: number; height: number },
    placement: string = 'auto',
    offset: { x: number; y: number } = { x: 0, y: 0 }
  ): CalloutPosition {
    const viewport = this.getViewportInfo();
    
    // If placement is auto, determine best position
    if (placement === 'auto') {
      placement = this.determineBestPlacement(targetPos, calloutSize, viewport);
    }
    
    const position = this.calculatePositionForPlacement(
      targetPos, 
      calloutSize, 
      placement as 'top' | 'bottom' | 'left' | 'right',
      offset
    );
    
    // Ensure callout stays within viewport
    const adjustedPosition = this.adjustForViewport(position, calloutSize, viewport);

    // Convert to viewport coordinates because TourOverlay is fixed to viewport
    const viewportPosition = {
      top: adjustedPosition.top - viewport.scrollTop,
      left: adjustedPosition.left - viewport.scrollLeft,
    };

    return {
      ...viewportPosition,
      placement: placement as 'top' | 'bottom' | 'left' | 'right',
      arrowPosition: this.calculateArrowPosition(targetPos, viewportPosition, placement, viewport)
    };
  }

  /**
   * Ensure target element is visible in viewport with smooth scrolling
   */
  static async ensureElementVisible(target: string | Element): Promise<void> {
    const element = typeof target === 'string' 
      ? document.querySelector(target) 
      : target;
    
    if (!element) {
      throw new Error(`Tour target not found: ${target}`);
    }

    const rect = element.getBoundingClientRect();
    const viewport = this.getViewportInfo();
    
    // Check if element is already visible
    if (this.isElementInViewport(rect, viewport)) {
      return;
    }

    // Calculate scroll position to center element in viewport
    const scrollTop = window.pageYOffset + rect.top - (viewport.height / 2) + (rect.height / 2);
    const scrollLeft = window.pageXOffset + rect.left - (viewport.width / 2) + (rect.width / 2);

    // Smooth scroll to position
    return new Promise((resolve) => {
      window.scrollTo({
        top: Math.max(0, scrollTop),
        left: Math.max(0, scrollLeft),
        behavior: 'smooth'
      });
      
      // Wait for scroll to complete
      setTimeout(resolve, 500);
    });
  }

  /**
   * Get viewport information
   */
  private static getViewportInfo() {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      scrollTop: window.pageYOffset || document.documentElement.scrollTop,
      scrollLeft: window.pageXOffset || document.documentElement.scrollLeft
    };
  }

  /**
   * Determine the best placement for a callout
   */
  private static determineBestPlacement(
    targetPos: ElementPosition,
    calloutSize: { width: number; height: number },
    viewport: any
  ): string {
    const spaceAbove = targetPos.top - viewport.scrollTop;
    const spaceBelow = viewport.height - (targetPos.top - viewport.scrollTop + targetPos.height);
    const spaceLeft = targetPos.left - viewport.scrollLeft;
    const spaceRight = viewport.width - (targetPos.left - viewport.scrollLeft + targetPos.width);

    // Prefer vertical placement if there's enough space
    if (spaceBelow >= calloutSize.height + this.CALLOUT_OFFSET + this.VIEWPORT_PADDING) {
      return 'bottom';
    }
    if (spaceAbove >= calloutSize.height + this.CALLOUT_OFFSET + this.VIEWPORT_PADDING) {
      return 'top';
    }
    
    // Fall back to horizontal placement
    if (spaceRight >= calloutSize.width + this.CALLOUT_OFFSET + this.VIEWPORT_PADDING) {
      return 'right';
    }
    if (spaceLeft >= calloutSize.width + this.CALLOUT_OFFSET + this.VIEWPORT_PADDING) {
      return 'left';
    }

    // Default to bottom if no ideal placement
    return 'bottom';
  }

  /**
   * Calculate position for a specific placement
   */
  private static calculatePositionForPlacement(
    targetPos: ElementPosition,
    calloutSize: { width: number; height: number },
    placement: 'top' | 'bottom' | 'left' | 'right',
    offset: { x: number; y: number }
  ) {
    let top: number;
    let left: number;

    switch (placement) {
      case 'top':
        top = targetPos.top - calloutSize.height - this.CALLOUT_OFFSET;
        left = targetPos.center.x - calloutSize.width / 2;
        break;
      case 'bottom':
        top = targetPos.top + targetPos.height + this.CALLOUT_OFFSET;
        left = targetPos.center.x - calloutSize.width / 2;
        break;
      case 'left':
        top = targetPos.center.y - calloutSize.height / 2;
        left = targetPos.left - calloutSize.width - this.CALLOUT_OFFSET;
        break;
      case 'right':
        top = targetPos.center.y - calloutSize.height / 2;
        left = targetPos.left + targetPos.width + this.CALLOUT_OFFSET;
        break;
    }

    return {
      top: top + offset.y,
      left: left + offset.x
    };
  }

  /**
   * Adjust position to keep callout within viewport
   */
  private static adjustForViewport(
    position: { top: number; left: number },
    calloutSize: { width: number; height: number },
    viewport: any
  ) {
    const adjustedPosition = { ...position };

    // Adjust horizontal position
    const rightEdge = position.left + calloutSize.width;
    const leftEdge = position.left;
    
    if (rightEdge > viewport.width - this.VIEWPORT_PADDING) {
      adjustedPosition.left = viewport.width - calloutSize.width - this.VIEWPORT_PADDING;
    }
    if (leftEdge < this.VIEWPORT_PADDING) {
      adjustedPosition.left = this.VIEWPORT_PADDING;
    }

    // Adjust vertical position
    const bottomEdge = position.top + calloutSize.height;
    const topEdge = position.top;
    
    if (bottomEdge > viewport.scrollTop + viewport.height - this.VIEWPORT_PADDING) {
      adjustedPosition.top = viewport.scrollTop + viewport.height - calloutSize.height - this.VIEWPORT_PADDING;
    }
    if (topEdge < viewport.scrollTop + this.VIEWPORT_PADDING) {
      adjustedPosition.top = viewport.scrollTop + this.VIEWPORT_PADDING;
    }

    return adjustedPosition;
  }

  /**
   * Calculate arrow position for callout
   */
  private static calculateArrowPosition(
    targetPos: ElementPosition,
    calloutPos: { top: number; left: number },
    placement: string,
    viewport?: any
  ) {
    switch (placement) {
      case 'top':
      case 'bottom': {
        const scrollLeft = viewport?.scrollLeft ?? (window.pageXOffset || document.documentElement.scrollLeft);
        const x = targetPos.center.x - (calloutPos.left + scrollLeft);
        return {
          x,
          y: placement === 'top' ? -this.ARROW_SIZE : 0
        };
      }
      case 'left':
      case 'right': {
        const scrollTop = viewport?.scrollTop ?? (window.pageYOffset || document.documentElement.scrollTop);
        const y = targetPos.center.y - (calloutPos.top + scrollTop);
        return {
          x: placement === 'left' ? -this.ARROW_SIZE : 0,
          y
        };
      }
      default:
        return { x: 0, y: 0 };
    }
  }

  /**
   * Check if element is visible in viewport
   */
  private static isElementInViewport(rect: DOMRect, viewport: any): boolean {
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= viewport.height &&
      rect.right <= viewport.width
    );
  }

  /**
   * Get z-index for tour overlays
   */
  static getTourZIndex(): number {
    // Ensure tour overlays appear above all other content
    // Rancher typically uses z-index values up to 9999
    return 10000;
  }

  /**
   * Create backdrop styles for element highlighting
   */
  static createBackdropStyles(targetPos: ElementPosition, backdropColor: string = 'rgba(0,0,0,0.5)') {
    return {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100vw',
      height: '100vh',
      backgroundColor: backdropColor,
      zIndex: this.getTourZIndex(),
      // Create cutout for highlighted element using clip-path
      clipPath: `polygon(
        0% 0%, 
        0% 100%, 
        ${targetPos.left}px 100%, 
        ${targetPos.left}px ${targetPos.top}px, 
        ${targetPos.left + targetPos.width}px ${targetPos.top}px, 
        ${targetPos.left + targetPos.width}px ${targetPos.top + targetPos.height}px, 
        ${targetPos.left}px ${targetPos.top + targetPos.height}px, 
        ${targetPos.left}px 100%, 
        100% 100%, 
        100% 0%
      )`
    };
  }
}
