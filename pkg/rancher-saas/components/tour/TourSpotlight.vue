<template>
  <div
    v-if="elementPosition"
    class="tour-spotlight"
    :class="spotlightClasses"
    :style="spotlightStyles"
  >
    <!-- Glow effect overlay -->
    <div
      v-if="highlightStyle === 'glow'"
      class="tour-spotlight__glow"
      :style="glowStyles"
    />
    
    <!-- Outline effect -->
    <div
      v-if="highlightStyle === 'outline'"
      class="tour-spotlight__outline"
      :style="outlineStyles"
    />
    
    <!-- Pulse animation for emphasis -->
    <div
      v-if="showPulse"
      class="tour-spotlight__pulse"
      :style="pulseStyles"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch, onMounted, onUnmounted } from 'vue';
import { TourPositioning } from './utils/positioning';
import type { ElementPosition } from '../../types/journey';

export default defineComponent({
  name: 'TourSpotlight',

  props: {
    target: {
      type: [String, Object],
      required: true,
    },
    highlightStyle: {
      type: String,
      default: 'outline',
      validator: (value: string) => ['outline', 'glow', 'backdrop', 'none'].includes(value),
    },
    offset: {
      type: Object,
      default: () => ({ x: 0, y: 0 }),
    },
    showPulse: {
      type: Boolean,
      default: true,
    },
    pulseDelay: {
      type: Number,
      default: 1000, // Start pulse after 1 second
    },
  },

  emits: ['element-ready', 'element-not-found'],

  setup(props, { emit }) {
    const elementPosition = ref<ElementPosition | null>(null);
    const isReady = ref(false);
    const pulseActive = ref(false);
    const resizeObserver = ref<ResizeObserver | null>(null);
    const mutationObserver = ref<MutationObserver | null>(null);

    return {
      elementPosition,
      isReady,
      pulseActive,
      resizeObserver,
      mutationObserver,
    };
  },

  computed: {
    spotlightClasses(): Record<string, boolean> {
      return {
        'tour-spotlight--outline': this.highlightStyle === 'outline',
        'tour-spotlight--glow': this.highlightStyle === 'glow',
        'tour-spotlight--backdrop': this.highlightStyle === 'backdrop',
        'tour-spotlight--pulse': this.pulseActive,
        'tour-spotlight--ready': this.isReady,
      };
    },

    spotlightStyles(): Record<string, any> {
      if (!this.elementPosition) return {};

      return {
        position: 'absolute',
        top: `${this.elementPosition.top + this.offset.y}px`,
        left: `${this.elementPosition.left + this.offset.x}px`,
        width: `${this.elementPosition.width}px`,
        height: `${this.elementPosition.height}px`,
        pointerEvents: 'none',
        zIndex: TourPositioning.getTourZIndex() + 1,
      };
    },

    outlineStyles(): Record<string, any> {
      return {
        position: 'absolute',
        top: '-3px',
        left: '-3px',
        right: '-3px',
        bottom: '-3px',
        border: '3px solid var(--primary)',
        borderRadius: '6px',
        boxShadow: '0 0 0 1px rgba(255, 255, 255, 0.8), 0 0 20px rgba(var(--primary-rgb), 0.3)',
        animation: 'tour-spotlight-outline 2s ease-in-out infinite',
      };
    },

    glowStyles(): Record<string, any> {
      return {
        position: 'absolute',
        top: '-8px',
        left: '-8px',
        right: '-8px',
        bottom: '-8px',
        background: 'radial-gradient(ellipse at center, rgba(var(--primary-rgb), 0.3) 0%, rgba(var(--primary-rgb), 0.1) 50%, transparent 70%)',
        borderRadius: '12px',
        animation: 'tour-spotlight-glow 3s ease-in-out infinite',
      };
    },

    pulseStyles(): Record<string, any> {
      return {
        position: 'absolute',
        top: '-6px',
        left: '-6px',
        right: '-6px',
        bottom: '-6px',
        border: '2px solid var(--primary)',
        borderRadius: '8px',
        opacity: '0',
        animation: 'tour-spotlight-pulse 2s ease-out infinite',
      };
    },
  },

  watch: {
    target: {
      handler() {
        this.updateElementPosition();
      },
      immediate: true,
    },
  },

  mounted() {
    this.setupObservers();
    this.startPulseTimer();
  },

  unmounted() {
    this.cleanupObservers();
  },

  methods: {
    async updateElementPosition() {
      try {
        this.isReady = false;
        
        // Wait for element to be available
        const element = await this.waitForElement();
        
        if (element) {
          this.elementPosition = TourPositioning.getElementPosition(element);
          this.isReady = true;
          this.$emit('element-ready', {
            element,
            position: this.elementPosition,
          });
        } else {
          this.$emit('element-not-found', { target: this.target });
        }
      } catch (error) {
        console.error('TourSpotlight: Failed to position element', error);
        this.$emit('element-not-found', { target: this.target, error });
      }
    },

    async waitForElement(maxAttempts: number = 10): Promise<Element | null> {
      for (let i = 0; i < maxAttempts; i++) {
        const element = typeof this.target === 'string' 
          ? document.querySelector(this.target)
          : this.target as Element;
        
        if (element) {
          return element;
        }
        
        // Wait 100ms before next attempt
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      return null;
    },

    setupObservers() {
      const element = typeof this.target === 'string' 
        ? document.querySelector(this.target)
        : this.target as Element;

      if (!element) return;

      // Watch for element size changes
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          this.updateElementPosition();
        });
        this.resizeObserver.observe(element);
      }

      // Watch for DOM changes that might affect element position
      this.mutationObserver = new MutationObserver(() => {
        this.updateElementPosition();
      });
      
      this.mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style'],
      });

      // Listen for window events
      window.addEventListener('resize', this.updateElementPosition);
      window.addEventListener('scroll', this.updateElementPosition, true);
    },

    cleanupObservers() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      }

      if (this.mutationObserver) {
        this.mutationObserver.disconnect();
        this.mutationObserver = null;
      }

      window.removeEventListener('resize', this.updateElementPosition);
      window.removeEventListener('scroll', this.updateElementPosition, true);
    },

    startPulseTimer() {
      if (this.showPulse && this.pulseDelay > 0) {
        setTimeout(() => {
          this.pulseActive = true;
        }, this.pulseDelay);
      } else if (this.showPulse) {
        this.pulseActive = true;
      }
    },
  },
});
</script>

<style lang="scss" scoped>
.tour-spotlight {
  transition: all 0.3s ease;

  &--ready {
    opacity: 1;
  }

  &__outline {
    transition: all 0.3s ease;
  }

  &__glow {
    transition: all 0.3s ease;
  }

  &__pulse {
    transition: all 0.3s ease;
  }
}

// Animations
@keyframes tour-spotlight-outline {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes tour-spotlight-glow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.05);
  }
}

@keyframes tour-spotlight-pulse {
  0% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .tour-spotlight {
    &__outline {
      border-color: currentColor;
      box-shadow: 0 0 0 2px white, 0 0 0 4px currentColor;
    }

    &__glow {
      background: currentColor;
      opacity: 0.3;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .tour-spotlight {
    &__outline,
    &__glow,
    &__pulse {
      animation: none;
    }
  }
}
</style>
