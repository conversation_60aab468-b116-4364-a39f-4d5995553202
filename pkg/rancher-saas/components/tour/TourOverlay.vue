<template>
  <Teleport to="body">
    <div
      v-if="isActive"
      ref="tourOverlay"
      class="tour-overlay"
      :class="overlayClasses"
      :style="overlayStyles"
      tabindex="-1"
      @keydown.esc="handleEscape"
      @click.self="handleBackdropClick"
    >
      <!-- Backdrop with cutout for highlighted element -->
      <div
        v-if="showBackdrop"
        class="tour-backdrop"
        :style="backdropStyles"
      />
      
      <!-- Spotlight highlight -->
      <TourSpotlight
        v-if="currentStep?.target"
        :target="currentStep.target"
        :highlight-style="currentStep.highlightStyle || 'outline'"
        :offset="currentStep.offset"
        @element-ready="handleElementReady"
      />
      
      <!-- Callout content -->
      <TourCallout
        v-if="currentStep?.calloutContent && elementPosition"
        :target="currentStep.target"
        :content="currentStep.calloutContent"
        :placement="currentStep.placement || 'auto'"
        :show-arrow="currentStep.arrow !== false"
        :element-position="elementPosition"
        :offset="currentStep.offset"
      />
      
      <!-- Navigation controls -->
      <TourNavigation
        v-if="showNavigation"
        :current-step="currentStepIndex"
        :total-steps="totalSteps"
        :can-go-back="canGoBack"
        :can-go-next="canGoNext"
        :step="currentStep"
        @next="nextStep"
        @back="previousStep"
        @skip="skipTour"
        @close="closeTour"
      />
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { mapGetters, mapActions } from 'vuex';

import TourSpotlight from './TourSpotlight.vue';
import TourCallout from './TourCallout.vue';
import TourNavigation from './TourNavigation.vue';

import { TourPositioning } from './utils/positioning';
import type { TourStep, TourDefinition, ElementPosition } from '../../types/journey';

export default defineComponent({
  name: 'TourOverlay',

  components: {
    TourSpotlight,
    TourCallout,
    TourNavigation,
  },

  props: {
    tourId: {
      type: String,
      default: null,
    },
    autoFocus: {
      type: Boolean,
      default: true,
    },
  },

  emits: [
    'tour-started',
    'tour-completed',
    'tour-skipped',
    'tour-closed',
    'step-changed',
    'element-highlighted',
  ],

  setup(props, { emit }) {
    const tourOverlay = ref<HTMLElement | null>(null);
    const elementPosition = ref<ElementPosition | null>(null);
    const isElementReady = ref(false);
    const previousFocusedElement = ref<HTMLElement | null>(null);

    return {
      tourOverlay,
      elementPosition,
      isElementReady,
      previousFocusedElement,
    };
  },

  computed: {
    ...mapGetters({
      currentTour: 'journeys/currentTour',
      currentStep: 'journeys/currentStep',
      currentStepIndex: 'journeys/getCurrentStepIndex',
      totalSteps: 'journeys/getTotalSteps',
      canGoBack: 'journeys/canGoPrevious',
      canGoNext: 'journeys/canGoNext',
      isActive: 'journeys/isTourActive',
    }),

    tour(): TourDefinition | null {
      return this.currentTour;
    },

    step(): TourStep | null {
      return this.currentStep as TourStep;
    },

    showBackdrop(): boolean {
      return this.tour?.overlay?.backdrop !== false;
    },

    showNavigation(): boolean {
      return this.step?.type !== 'custom' || this.step?.showNavigation !== false;
    },

    overlayClasses(): Record<string, boolean> {
      return {
        'tour-overlay--active': this.isActive,
        'tour-overlay--backdrop': this.showBackdrop,
        'tour-overlay--no-backdrop': !this.showBackdrop,
      };
    },

    overlayStyles(): Record<string, any> {
      return {
        zIndex: TourPositioning.getTourZIndex(),
        '--tour-backdrop-color': this.tour?.overlay?.backdropColor || 'rgba(0, 0, 0, 0.5)',
      };
    },

    backdropStyles(): Record<string, any> | null {
      if (!this.showBackdrop || !this.elementPosition) {
        return {
          position: 'fixed',
          top: '0',
          left: '0',
          width: '100vw',
          height: '100vh',
          backgroundColor: this.tour?.overlay?.backdropColor || 'rgba(0, 0, 0, 0.5)',
          zIndex: TourPositioning.getTourZIndex(),
        };
      }

      return TourPositioning.createBackdropStyles(
        this.elementPosition,
        this.tour?.overlay?.backdropColor
      );
    },
  },

  watch: {
    currentStep: {
      handler(newStep: TourStep | null) {
        if (newStep) {
          this.handleStepChange(newStep);
        }
      },
      immediate: true,
    },

    isActive: {
      handler(isActive: boolean) {
        if (isActive) {
          this.handleTourStart();
        } else {
          this.handleTourEnd();
        }
      },
      immediate: true,
    },
  },

  mounted() {
    this.setupEventListeners();
  },

  unmounted() {
    this.cleanupEventListeners();
    this.restoreFocus();
  },

  methods: {
    ...mapActions({
      nextStepAction: 'journeys/nextStep',
      previousStepAction: 'journeys/previousStep',
      skipTourAction: 'journeys/skipJourney',
      closeTourAction: 'journeys/completeJourney',
    }),

    async handleStepChange(step: TourStep) {
      this.isElementReady = false;
      this.elementPosition = null;

      if (step.target) {
        try {
          // Ensure element is visible
          await TourPositioning.ensureElementVisible(step.target);
          
          // Get element position
          this.elementPosition = TourPositioning.getElementPosition(step.target);
          this.isElementReady = true;

          this.$emit('element-highlighted', {
            step,
            element: step.target,
            position: this.elementPosition,
          });

          // Auto-advance if configured
          if (step.autoAdvance && step.autoAdvance > 0) {
            setTimeout(() => {
              this.nextStep();
            }, step.autoAdvance * 1000);
          }
        } catch (error) {
          console.error('Tour: Failed to highlight element', error);
          // Continue tour even if element highlighting fails
          this.isElementReady = true;
        }
      } else {
        this.isElementReady = true;
      }

      this.$emit('step-changed', { step, index: this.currentStepIndex });
    },

    handleElementReady() {
      this.isElementReady = true;
    },

    handleTourStart() {
      // Store current focus to restore later
      this.previousFocusedElement = document.activeElement as HTMLElement;
      
      // Focus tour overlay for keyboard navigation
      if (this.autoFocus) {
        nextTick(() => {
          this.tourOverlay?.focus();
        });
      }

      // Prevent body scroll
      document.body.style.overflow = 'hidden';

      this.$emit('tour-started', { tour: this.tour });
    },

    handleTourEnd() {
      // Restore body scroll
      document.body.style.overflow = '';
      
      // Restore focus
      this.restoreFocus();
    },

    handleEscape() {
      if (this.tour?.overlay?.closeOnEscape !== false) {
        this.closeTour();
      }
    },

    handleBackdropClick() {
      if (this.tour?.overlay?.closeOnClickOutside !== false) {
        this.closeTour();
      }
    },

    nextStep() {
      this.nextStepAction();
    },

    previousStep() {
      this.previousStepAction();
    },

    skipTour() {
      this.skipTourAction();
      this.$emit('tour-skipped', { tour: this.tour });
    },

    closeTour() {
      this.closeTourAction();
      this.$emit('tour-closed', { tour: this.tour });
    },

    setupEventListeners() {
      // Handle window resize
      window.addEventListener('resize', this.handleResize);
      window.addEventListener('scroll', this.handleScroll);
    },

    cleanupEventListeners() {
      window.removeEventListener('resize', this.handleResize);
      window.removeEventListener('scroll', this.handleScroll);
    },

    handleResize() {
      // Recalculate positions on resize
      if (this.currentStep?.target) {
        this.elementPosition = TourPositioning.getElementPosition(this.currentStep.target);
      }
    },

    handleScroll() {
      // Recalculate positions on scroll
      if (this.currentStep?.target) {
        this.elementPosition = TourPositioning.getElementPosition(this.currentStep.target);
      }
    },

    restoreFocus() {
      if (this.previousFocusedElement) {
        this.previousFocusedElement.focus();
        this.previousFocusedElement = null;
      }
    },
  },
});
</script>

<style lang="scss" scoped>
.tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  outline: none;

  &--active {
    pointer-events: auto;
  }

  &--backdrop {
    .tour-backdrop {
      pointer-events: auto;
    }
  }
}

.tour-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}

// Ensure tour content appears above backdrop
:deep(.tour-callout),
:deep(.tour-navigation),
:deep(.tour-spotlight) {
  position: relative;
  z-index: 1;
}
</style>
