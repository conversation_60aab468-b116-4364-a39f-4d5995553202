[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Build Custom PLG Journey System Foundation DESCRIPTION:Create custom tour/journey system using <PERSON><PERSON>'s Wizard component as foundation. Support both educational tours and PLG user journeys with decision trees, workflow integration, and progress tracking across sessions.
-[x] NAME:Implement PLG Journey Store Module DESCRIPTION:Create Vuex store module for managing PLG journeys, user progress, decision trees, and workflow states. Support complex journey definitions with branching logic, conditional steps, and integration with Rancher functionality.
-[x] NAME:Create Journey Progress Persistence DESCRIPTION:Implement journey state persistence using <PERSON><PERSON>'s user preferences API. This is a store. Check other implementations in the system for examples how to use. Track user progress across multiple sessions, completed workflows, and decision points. Support journey resumption and progress analytics.
-[x] NAME:Build PLG Journey Components DESCRIPTION:Create Vue components for PLG journeys: decision trees, workflow steps, progress indicators, and action triggers. Extend Rancher's Wizard component to support branching workflows and integration with actual Rancher functionality.
--[x] NAME:Build Tour Overlay System Foundation DESCRIPTION:Create the core overlay system for application tours with element highlighting, positioning, and z-index management. This will serve as the foundation for all tour callouts and feature highlights.
--[ ] NAME:Implement Element Highlighting and Spotlight DESCRIPTION:Create components for highlighting specific UI elements with spotlight effects, backdrop overlays, and smooth animations. Support different highlight styles (outline, glow, backdrop) and positioning.
--[ ] NAME:Build Tour Callout and Tooltip Components DESCRIPTION:Create smart callout components that can point to specific elements with arrows, positioning logic, and responsive behavior. Extend existing Rancher tooltip system for tour-specific needs.
--[ ] NAME:Create Tour Step Navigation System DESCRIPTION:Build navigation components for moving between tour steps with smooth transitions, progress indicators, and skip/back/next functionality. Integrate with existing journey progress tracking.
--[ ] NAME:Implement Tour Definition and Configuration System DESCRIPTION:Create a flexible system for defining tours with step configurations, element selectors, positioning preferences, and conditional logic. Support both code-based and JSON-based tour definitions.
--[ ] NAME:Build Tour Lifecycle Management DESCRIPTION:Implement tour triggering, state management, and lifecycle hooks. Handle tour activation based on user state, page navigation, and feature flags. Integrate with existing journey store.
--[ ] NAME:Add Tour Analytics and Tracking DESCRIPTION:Implement analytics for tour engagement, completion rates, step drop-offs, and user interactions. Track which features users discover through tours and measure tour effectiveness.
--[ ] NAME:Create Tour Testing and Documentation DESCRIPTION:Write comprehensive tests for tour functionality, create documentation for adding new tours, and build a tour preview/testing interface for developers and content creators.
-[ ] NAME:Implement Workflow Integration System DESCRIPTION:Create integration layer between PLG journeys and actual Rancher functionality. Enable journeys to trigger real actions like creating clusters, credentials, users, and navigating to specific workflows. Build action execution engine with error handling and progress tracking.
-[ ] NAME:Add PLG Journey Configuration System DESCRIPTION:Create journey definition system supporting decision trees, conditional logic, workflow integration, and progress tracking. Enable configuration of complex PLG flows like onboarding, feature adoption, and user activation journeys.
-[ ] NAME:Add Journey Analytics and Tracking DESCRIPTION:Implement analytics system to track journey completion rates, user paths, drop-off points, and decision choices. Create dashboard for monitoring PLG journey effectiveness and identifying optimization opportunities. Support A/B testing of different journey flows.
-[ ] NAME:Create PLG Journey Testing and Documentation DESCRIPTION:Write comprehensive unit tests for PLG journey functionality including decision trees, workflow integration, and state management. Create documentation for adding new journeys and PLG flows. Test integration with existing Rancher Dashboard features to ensure no conflicts. Include journey analytics testing and performance validation.
-[ ] NAME:Implement i18n Support DESCRIPTION:Add internationalization support for tour content using existing Rancher i18n patterns. Create translation keys for tour text, buttons, and messages following established conventions.